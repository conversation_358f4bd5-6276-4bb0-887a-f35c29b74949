"""
Composio OAuth Connection Manager

This service manages OAuth connections for Composio integrations, implementing
the authentication flow described in composio_auth.md.

Key responsibilities:
1. Initiate OAuth connections using entity.initiate_connection()
2. <PERSON>le redirect URLs for user authentication
3. Wait for connection activation using wait_until_active()
4. Store connection metadata and status
5. Generate MCP URLs with connected_account_id
"""

import asyncio
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timedelta
from utils.logger import logger
from utils.config import config
from supabase import create_client, Client
from services.composio_entity_manager import composio_entity_manager, EntityInfo
from constants.composio_mcp_constants import (
    get_composio_integration_id,
    get_composio_mcp_url_with_connected_account,
    get_composio_app_config,
)

try:
    from composio import ComposioToolSet
    from composio.exceptions import SDKTimeoutError

    Entity = None  # Entity objects are obtained from toolset.get_entity()
except ImportError:
    logger.warning(
        "composio-core not installed. Install with: pip install composio-core"
    )
    ComposioToolSet = None
    SDKTimeoutError = None
    Entity = None


@dataclass
class ConnectionInfo:
    """Information about a Composio OAuth connection"""

    connection_id: Optional[str]
    user_id: str
    entity_id: str
    app_key: str
    integration_id: str
    status: str  # pending, active, failed, expired
    redirect_url: Optional[str]
    connected_account_id: Optional[str]
    mcp_url: Optional[str]
    expires_at: Optional[datetime]
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ConnectionResult:
    """Result of connection initiation"""

    success: bool
    connection_info: Optional[ConnectionInfo] = None
    error: Optional[str] = None


class ComposioOAuthManager:
    """
    Service for managing Composio OAuth connections.

    This implements the OAuth flow from composio_auth.md:
    1. Get entity for user
    2. Initiate connection with integration_id
    3. Get redirect URL for user authentication
    4. Wait for connection activation
    5. Generate MCP URL with connected_account_id
    """

    def __init__(self):
        """Initialize the OAuth manager with Composio SDK and database connection."""
        if not ComposioToolSet:
            logger.warning(
                "composio-core package not available. Some features will be disabled."
            )
            self.toolset = None
        else:
            # Initialize Composio SDK
            self.composio_api_key = config.COMPOSIO_API_KEY
            if not self.composio_api_key:
                logger.warning(
                    "COMPOSIO_API_KEY not set. Composio features will be disabled."
                )
                self.toolset = None
            else:
                self.toolset = ComposioToolSet(api_key=self.composio_api_key)

        # Initialize Supabase client
        self.supabase: Client = create_client(
            config.SUPABASE_URL, config.SUPABASE_SERVICE_ROLE_KEY
        )

        logger.info("Composio OAuth Manager initialized successfully")

    async def initiate_connection(self, user_id: str, app_key: str) -> ConnectionResult:
        """
        Initiate OAuth connection for a user and app.

        This implements the flow from composio_auth.md:
        1. Get entity for user
        2. Use toolset.initiate_connection(integration_id, entity_id)
        3. Return redirect URL for user authentication

        Args:
            user_id: The user's UUID
            app_key: The app key (e.g., "gmail", "slack", "notion")

        Returns:
            ConnectionResult with redirect URL or error
        """
        try:
            logger.info(
                f"Initiating OAuth connection for user {user_id}, app {app_key}"
            )

            # Step 1: Get integration ID for the app
            integration_id = get_composio_integration_id(app_key)
            if not integration_id:
                return ConnectionResult(
                    success=False, error=f"No integration ID found for app {app_key}"
                )

            # Step 2: Get or create entity for user
            entity_info = await composio_entity_manager.get_or_create_entity(user_id)
            if not entity_info:
                return ConnectionResult(
                    success=False, error=f"Failed to get entity for user {user_id}"
                )

            # Step 3: Get Composio entity object
            entity = composio_entity_manager.get_composio_entity(entity_info.entity_id)
            if not entity:
                return ConnectionResult(
                    success=False,
                    error=f"Failed to get Composio entity {entity_info.entity_id}",
                )

            # Step 4: Check if connection already exists
            existing_connection = await self._get_stored_connection(user_id, app_key)
            if existing_connection and existing_connection.status == "active":
                logger.info(
                    f"Active connection already exists for user {user_id}, app {app_key}"
                )
                return ConnectionResult(
                    success=True, connection_info=existing_connection
                )

            # Step 5: Initiate connection using Composio SDK
            logger.info(f"Initiating connection with integration_id {integration_id}")

            # Check if toolset is available
            if not self.toolset:
                return ConnectionResult(
                    success=False, error="Composio SDK not properly initialized"
                )

            # This is the key call from composio_auth.md:
            # Use toolset.initiate_connection() instead of entity.initiate_connection()
            connection_request = self.toolset.initiate_connection(
                integration_id=integration_id,
                entity_id=entity_info.entity_id,
                redirect_url="https://atlasagents.ai/dashboard",
            )

            # Extract redirect URL
            redirect_url = connection_request.redirectUrl
            if not redirect_url:
                return ConnectionResult(
                    success=False, error="No redirect URL returned from Composio"
                )

            # Step 6: Store connection information
            connection_info = ConnectionInfo(
                connection_id=None,  # Will be set after activation
                user_id=user_id,
                entity_id=entity_info.entity_id,
                app_key=app_key,
                integration_id=integration_id,
                status="pending",
                redirect_url=redirect_url,
                connected_account_id=None,  # Will be set after activation
                mcp_url=None,  # Will be generated after activation
                expires_at=datetime.now() + timedelta(minutes=30),  # 30 min expiry
                metadata={
                    "connection_request_id": getattr(connection_request, "id", None),
                    "initiated_at": datetime.now().isoformat(),
                    # Note: connection_request object removed to avoid JSON serialization issues
                    # We'll retrieve it using the connection_request_id when needed
                },
            )

            await self._store_connection(connection_info)

            # Store connection_request object in memory for wait_until_active
            # We'll use a simple in-memory cache with the connection_request_id as key
            if not hasattr(self, "_connection_requests"):
                self._connection_requests = {}

            connection_request_id = getattr(connection_request, "id", None)
            if connection_request_id:
                self._connection_requests[connection_request_id] = connection_request
                logger.info(
                    f"Stored connection_request {connection_request_id} in memory for wait_until_active"
                )

            logger.info(
                f"Successfully initiated connection for {app_key}, redirect URL: {redirect_url}"
            )
            return ConnectionResult(success=True, connection_info=connection_info)

        except Exception as e:
            logger.error(
                f"Error initiating connection for user {user_id}, app {app_key}: {e}"
            )
            return ConnectionResult(success=False, error=str(e))

    async def wait_for_connection_activation(
        self, user_id: str, app_key: str, timeout: int = 180
    ) -> ConnectionResult:
        """
        Wait for OAuth connection to become active.

        This implements the wait_until_active pattern from Composio docs:
        active_connection = connection_request.wait_until_active(client=toolset.client, timeout=180)

        Args:
            user_id: The user's UUID
            app_key: The app key
            timeout: Timeout in seconds (default 180 = 3 minutes)

        Returns:
            ConnectionResult with active connection info or error
        """
        try:
            logger.info(
                f"Waiting for connection activation for user {user_id}, app {app_key}"
            )

            # Get stored connection
            connection_info = await self._get_stored_connection(user_id, app_key)
            if not connection_info:
                return ConnectionResult(
                    success=False, error="No pending connection found"
                )

            if connection_info.status == "active":
                logger.info(
                    f"Connection already active for user {user_id}, app {app_key}"
                )
                return ConnectionResult(success=True, connection_info=connection_info)

            # Get stored connection_request from in-memory cache
            connection_request_id = connection_info.metadata.get(
                "connection_request_id"
            )
            connection_request = None

            if hasattr(self, "_connection_requests") and connection_request_id:
                connection_request = self._connection_requests.get(
                    connection_request_id
                )

            if not connection_request:
                logger.warning(
                    f"Connection request {connection_request_id} not found in memory cache"
                )
                return ConnectionResult(
                    success=False,
                    error="Connection request not found. Please initiate connection again.",
                )

            # Use the correct wait_until_active from Composio SDK as per documentation
            logger.info(
                f"Waiting for user authorization and connection activation (timeout: {timeout}s)"
            )
            logger.info(
                "Using connection_request.wait_until_active() as per Composio documentation"
            )

            try:
                # Use the correct Composio SDK method as per documentation
                # This is the proper way to wait for OAuth completion
                active_connection = connection_request.wait_until_active(
                    client=self.toolset.client,  # Pass the Composio client instance
                    timeout=float(timeout),  # Wait up to specified timeout
                )

                # Extract connected_account_id from the active connection
                # According to Composio docs: active_connection.id is the connected_account_id
                connected_account_id = active_connection.id
                logger.info(
                    f"Success! Connection is ACTIVE. Connected Account ID: {connected_account_id}"
                )

                # Clean up the in-memory cache
                if hasattr(self, "_connection_requests") and connection_request_id:
                    del self._connection_requests[connection_request_id]
                    logger.info(
                        f"Cleaned up connection_request {connection_request_id} from memory"
                    )

                # Update connection info with active status and connected_account_id
                connection_info.status = "active"
                connection_info.connection_id = getattr(
                    active_connection, "connectionId", connected_account_id
                )
                connection_info.connected_account_id = connected_account_id

                # Generate MCP URL with connected_account_id (CORRECT FORMAT)
                mcp_url = get_composio_mcp_url_with_connected_account(
                    app_key, connected_account_id
                )
                connection_info.mcp_url = mcp_url

                # Update in database
                await self._update_connection(connection_info)

                # 🔥 CRITICAL: Store FINAL MCP URL in agents table with tools
                try:
                    from services.composio_integration import composio_mcp_service

                    # Store the FINAL MCP URL with connected_account_id
                    agent_storage_success = (
                        await self._store_final_mcp_connection_with_tools(
                            user_id, app_key, mcp_url, connected_account_id
                        )
                    )

                    if agent_storage_success:
                        logger.info(
                            f"✅ Successfully stored FINAL MCP URL with tools in agents table for {app_key}"
                        )
                    else:
                        logger.warning(
                            f"⚠️ Failed to store MCP URL in agents table for {app_key}"
                        )
                except Exception as agent_error:
                    logger.error(
                        f"❌ Error storing MCP URL in agents table: {agent_error}"
                    )
                    # Don't fail the entire flow if agent storage fails

                logger.info(
                    f"Connection activated for user {user_id}, app {app_key}, MCP URL: {mcp_url}"
                )
                return ConnectionResult(success=True, connection_info=connection_info)

            except Exception as sdk_error:
                # Handle SDKTimeoutError and other exceptions
                if SDKTimeoutError and isinstance(sdk_error, SDKTimeoutError):
                    logger.warning(
                        f"OAuth completion timeout for user {user_id}, app {app_key}"
                    )
                    error_msg = "Connection did not become active within timeout period"
                else:
                    logger.error(f"OAuth completion error: {sdk_error}")
                    error_msg = f"OAuth completion failed: {str(sdk_error)}"

                # Clean up the in-memory cache on error
                if hasattr(self, "_connection_requests") and connection_request_id:
                    del self._connection_requests[connection_request_id]
                    logger.info(
                        f"Cleaned up connection_request {connection_request_id} from memory after error"
                    )

                # Update connection status to failed
                connection_info.status = "failed"
                await self._update_connection(connection_info)

                return ConnectionResult(success=False, error=error_msg)

        except Exception as e:
            logger.error(f"Error waiting for connection activation: {e}")
            return ConnectionResult(success=False, error=str(e))

    async def _store_final_mcp_connection_with_tools(
        self, user_id: str, app_key: str, mcp_url: str, connected_account_id: str
    ) -> bool:
        """
        Store the final MCP connection with tools in the agents table.

        This method:
        1. Stores the final MCP URL with connected_account_id format
        2. Discovers available tools from the MCP server
        3. Populates the enabledTools array with all available tools

        Args:
            user_id: The user's UUID
            app_key: The app key (e.g., "gmail", "slack")
            mcp_url: The final MCP URL with connected_account_id
            connected_account_id: The connected account ID from Composio

        Returns:
            True if successful, False otherwise
        """
        try:
            from services.composio_integration import composio_mcp_service
            from services.mcp_custom import discover_custom_tools

            logger.info(f"Storing final MCP connection with tools for {app_key}")

            # Step 1: Discover available tools from the MCP server
            try:
                discovery_result = await discover_custom_tools(
                    request_type="http", config={"url": mcp_url}
                )
                available_tools = [
                    tool["name"] for tool in discovery_result.get("tools", [])
                ]
                logger.info(
                    f"Discovered {len(available_tools)} tools for {app_key}: {available_tools}"
                )
            except Exception as discovery_error:
                logger.warning(
                    f"Failed to discover tools for {app_key}: {discovery_error}"
                )
                available_tools = []  # Continue without tools if discovery fails

            # Step 2: Store the MCP connection with tools
            session_uuid = f"oauth_{user_id}_{app_key}_{connected_account_id[:8]}"

            # Get user's account and default agent
            actual_user_id = composio_mcp_service._normalize_user_id(user_id)
            account_result = (
                composio_mcp_service.supabase.schema("basejump")
                .table("accounts")
                .select("id")
                .eq("primary_owner_user_id", actual_user_id)
                .eq("personal_account", True)
                .execute()
            )

            if not account_result.data:
                logger.error(f"No personal account found for user {user_id}")
                return False

            account_id = account_result.data[0]["id"]

            # Get or create default agent
            default_agent_result = (
                composio_mcp_service.supabase.table("agents")
                .select("agent_id, custom_mcps")
                .eq("account_id", account_id)
                .eq("is_default", True)
                .execute()
            )

            if not default_agent_result.data:
                # Create default agent if none exists
                from utils.default_agent_config import get_default_agent_config

                create_result = (
                    composio_mcp_service.supabase.table("agents")
                    .insert(get_default_agent_config(account_id))
                    .execute()
                )
                if not create_result.data:
                    logger.error(
                        f"Failed to create default agent for account {account_id}"
                    )
                    return False
                default_agent_id = create_result.data[0]["agent_id"]
                current_custom_mcps = []
            else:
                default_agent_id = default_agent_result.data[0]["agent_id"]
                current_custom_mcps = default_agent_result.data[0]["custom_mcps"] or []

            # Step 3: Create or update MCP config with tools
            new_mcp_config = {
                "name": app_key.title(),
                "type": "http",
                "config": {"url": mcp_url},  # Final URL with connected_account_id
                "enabledTools": available_tools,  # All discovered tools
                "created_at": "now()",
            }

            # Find and update existing entry or add new one
            existing_index = None
            for i, mcp in enumerate(current_custom_mcps):
                if (
                    mcp.get("type") == "http"
                    and mcp.get("name", "").lower() == app_key.lower()
                ):
                    existing_index = i
                    break

            if existing_index is not None:
                current_custom_mcps[existing_index] = new_mcp_config
                logger.info(
                    f"Updated existing Composio MCP for {app_key} with {len(available_tools)} tools"
                )
            else:
                current_custom_mcps.append(new_mcp_config)
                logger.info(
                    f"Added new Composio MCP for {app_key} with {len(available_tools)} tools"
                )

            # Step 4: Update the default agent
            update_result = (
                composio_mcp_service.supabase.table("agents")
                .update({"custom_mcps": current_custom_mcps})
                .eq("agent_id", default_agent_id)
                .execute()
            )

            if not update_result.data:
                logger.error(
                    f"Failed to update default agent {default_agent_id} with final MCP config"
                )
                return False

            logger.info(
                f"Successfully stored final MCP connection for {app_key} with {len(available_tools)} tools"
            )
            return True

        except Exception as e:
            logger.error(f"Error storing final MCP connection with tools: {e}")
            return False

    async def get_connection(
        self, user_id: str, app_key: str
    ) -> Optional[ConnectionInfo]:
        """
        Get connection information for user and app.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            ConnectionInfo if found, None otherwise
        """
        try:
            return await self._get_stored_connection(user_id, app_key)
        except Exception as e:
            logger.error(
                f"Error getting connection for user {user_id}, app {app_key}: {e}"
            )
            return None

    async def list_user_connections(self, user_id: str) -> List[ConnectionInfo]:
        """
        List all connections for a user from agents table.

        Args:
            user_id: The user's UUID

        Returns:
            List of ConnectionInfo objects
        """
        try:
            # Get connections from agents table custom_mcps instead
            from services.composio_integration import composio_mcp_service

            connections_data = await composio_mcp_service.list_user_mcp_connections(
                user_id
            )
            connections = []

            for conn_data in connections_data:
                if "mcp.composio.dev" in conn_data.get("mcp_url", ""):
                    # Convert to ConnectionInfo format
                    connection_info = ConnectionInfo(
                        connection_id=conn_data.get("id"),
                        user_id=user_id,
                        entity_id="",  # Not stored in agents table
                        app_key=conn_data.get("app_key", ""),
                        integration_id="",  # Not stored in agents table
                        status=(
                            "active"
                            if conn_data.get("status") == "connected"
                            else "pending"
                        ),
                        redirect_url=None,
                        connected_account_id=None,  # Extract from URL if needed
                        mcp_url=conn_data.get("mcp_url"),
                        expires_at=None,
                        metadata={},
                    )
                    connections.append(connection_info)

            return connections

        except Exception as e:
            logger.error(f"Error listing connections for user {user_id}: {e}")
            return []

    async def delete_connection(self, user_id: str, app_key: str) -> bool:
        """
        Delete a connection for user and app from agents table.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete from agents table custom_mcps instead
            from services.composio_integration import composio_mcp_service

            success = await composio_mcp_service.delete_user_mcp_connection(
                user_id, app_key
            )

            if success:
                logger.info(
                    f"Successfully deleted connection for user {user_id}, app {app_key}"
                )
                # Also clean up in-memory cache
                if hasattr(self, "_pending_connections"):
                    connection_key = f"{user_id}:{app_key}"
                    self._pending_connections.pop(connection_key, None)
                return True
            else:
                logger.warning(
                    f"No connection found to delete for user {user_id}, app {app_key}"
                )
                return False

        except Exception as e:
            logger.error(
                f"Error deleting connection for user {user_id}, app {app_key}: {e}"
            )
            return False

    async def _get_stored_connection(
        self, user_id: str, app_key: str
    ) -> Optional[ConnectionInfo]:
        """Get connection from in-memory cache (since we don't use composio_connections table)."""
        try:
            # Since we removed the composio_connections table, we'll use in-memory storage
            # for the OAuth flow state. The final connection is stored in agents table.
            if not hasattr(self, "_pending_connections"):
                self._pending_connections = {}

            connection_key = f"{user_id}:{app_key}"
            return self._pending_connections.get(connection_key)

        except Exception as e:
            logger.error(f"Error getting stored connection: {e}")
            return None

    async def _store_connection(self, connection_info: ConnectionInfo) -> bool:
        """Store connection in in-memory cache (since we don't use composio_connections table)."""
        try:
            # Store in in-memory cache for OAuth flow state tracking
            if not hasattr(self, "_pending_connections"):
                self._pending_connections = {}

            connection_key = f"{connection_info.user_id}:{connection_info.app_key}"
            self._pending_connections[connection_key] = connection_info

            logger.info(
                f"Successfully stored connection in memory for user {connection_info.user_id}, app {connection_info.app_key}"
            )
            return True

        except Exception as e:
            logger.error(f"Error storing connection: {e}")
            return False

    async def _update_connection(self, connection_info: ConnectionInfo) -> bool:
        """Update connection in in-memory cache (since we don't use composio_connections table)."""
        try:
            # Update in in-memory cache
            if not hasattr(self, "_pending_connections"):
                self._pending_connections = {}

            connection_key = f"{connection_info.user_id}:{connection_info.app_key}"
            self._pending_connections[connection_key] = connection_info

            logger.info(
                f"Successfully updated connection in memory for user {connection_info.user_id}, app {connection_info.app_key}"
            )
            return True

        except Exception as e:
            logger.error(f"Error updating connection: {e}")
            return False

    def _connection_from_db(self, conn_data: Dict[str, Any]) -> ConnectionInfo:
        """Convert database row to ConnectionInfo object."""
        expires_at = None
        if conn_data.get("expires_at"):
            expires_at = datetime.fromisoformat(
                conn_data["expires_at"].replace("Z", "+00:00")
            )

        return ConnectionInfo(
            connection_id=conn_data.get("connection_id"),
            user_id=conn_data["user_id"],
            entity_id=conn_data["entity_id"],
            app_key=conn_data["app_key"],
            integration_id=conn_data["integration_id"],
            status=conn_data["status"],
            redirect_url=conn_data.get("redirect_url"),
            connected_account_id=conn_data.get("connected_account_id"),
            mcp_url=conn_data.get("mcp_url"),
            expires_at=expires_at,
            metadata=conn_data.get("metadata"),
        )

    async def check_connection_status(
        self, user_id: str, app_key: str
    ) -> Optional[str]:
        """
        Check the current status of a connection.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            Connection status string or None if not found
        """
        try:
            connection_info = await self._get_stored_connection(user_id, app_key)
            return connection_info.status if connection_info else None
        except Exception as e:
            logger.error(f"Error checking connection status: {e}")
            return None

    async def refresh_connection_status(self, user_id: str, app_key: str) -> bool:
        """
        Refresh connection status by checking with Composio API.

        This method would typically query the Composio API to get the latest
        connection status and update our local database.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            True if refresh was successful, False otherwise
        """
        try:
            logger.info(
                f"Refreshing connection status for user {user_id}, app {app_key}"
            )

            connection_info = await self._get_stored_connection(user_id, app_key)
            if not connection_info:
                logger.warning(
                    f"No connection found to refresh for user {user_id}, app {app_key}"
                )
                return False

            # In a real implementation, you would:
            # 1. Query Composio API for connection status
            # 2. Update local database with latest status
            # 3. Handle token refresh if needed

            # For now, we'll just log that refresh was attempted
            logger.info(f"Connection status refresh completed for {app_key}")
            return True

        except Exception as e:
            logger.error(f"Error refreshing connection status: {e}")
            return False

    async def expire_connection(self, user_id: str, app_key: str) -> bool:
        """
        Mark a connection as expired by removing it from agents table.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            True if successful, False otherwise
        """
        try:
            # Since we don't have a status field in agents table, we'll just delete the connection
            success = await self.delete_connection(user_id, app_key)

            if success:
                logger.info(
                    f"Successfully expired (deleted) connection for user {user_id}, app {app_key}"
                )
                return True
            else:
                logger.warning(
                    f"No connection found to expire for user {user_id}, app {app_key}"
                )
                return False

        except Exception as e:
            logger.error(f"Error expiring connection: {e}")
            return False

    async def reactivate_connection(
        self, user_id: str, app_key: str
    ) -> ConnectionResult:
        """
        Reactivate an expired or failed connection.

        This will initiate a new OAuth flow for the connection.

        Args:
            user_id: The user's UUID
            app_key: The app key

        Returns:
            ConnectionResult with new redirect URL or error
        """
        try:
            logger.info(f"Reactivating connection for user {user_id}, app {app_key}")

            # Delete existing connection
            await self.delete_connection(user_id, app_key)

            # Initiate new connection
            return await self.initiate_connection(user_id, app_key)

        except Exception as e:
            logger.error(f"Error reactivating connection: {e}")
            return ConnectionResult(success=False, error=str(e))


# Create singleton instance
composio_oauth_manager = ComposioOAuthManager()
