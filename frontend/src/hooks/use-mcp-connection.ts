/**
 * Hook for handling MCP server connections and authentication flows
 * Extracted from the dashboard MCP carousel to be reusable across components
 */

import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { ComposioMCPService } from '@/lib/composio-api';
import { createClient } from '@/lib/supabase/client';
import { ComposioApp } from '@/types/composio';
import { useQueryClient } from '@tanstack/react-query';
import { agentKeys } from '@/hooks/react-query/agents/keys';

interface UseMCPConnectionOptions {
  onConnectionSuccess?: (appKey: string, appName: string) => void;
  onConnectionError?: (appKey: string, appName: string, error: Error) => void;
}

export function useMCPConnection(options: UseMCPConnectionOptions = {}) {
  const queryClient = useQueryClient();
  const [connectingApps, setConnectingApps] = useState<Set<string>>(new Set());

  // Helper function to get authenticated headers
  const getAuthHeaders = useCallback(async () => {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('No authentication token available. Please sign in.');
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    };
  }, []);

  // Handle MCP server connection with full authentication flow
  const connectToMCPServer = useCallback(async (app: ComposioApp) => {
    const { key: appKey, name: appName } = app;

    if (connectingApps.has(appKey)) return;

    setConnectingApps(prev => new Set(prev).add(appKey));

    try {
      const API_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_BACKEND_URL?.replace(/\/api$/, '') || '';
      const authHeaders = await getAuthHeaders();

      // NEW SINGLE ENDPOINT FLOW: Complete OAuth flow in one call
      console.log(`[MCP] Starting complete OAuth flow for ${appName}...`);

      const authResponse = await fetch(`${API_URL}/api/composio-mcp/connect-complete`, {
        method: 'POST',
        headers: authHeaders,
        body: JSON.stringify({
          app_key: appKey,
          wait_for_completion: true,  // Wait for complete OAuth flow
          timeout: 180  // 3 minutes timeout
        }),
      });

      if (!authResponse.ok) {
        const errorText = await authResponse.text();
        throw new Error(`Failed to complete OAuth flow: ${errorText}`);
      }

      const authData = await authResponse.json();

      if (authData.success) {
        if (authData.status === 'redirect') {
          // First phase: redirect user to OAuth
          toast.success("Redirecting to Authentication", {
            description: `Opening ${appName} authentication. You'll be redirected back after completion.`,
          });
          window.open(authData.redirect_url, '_blank');
        } else if (authData.status === 'completed') {
          // OAuth completed successfully
          toast.success("Connection Successful!", {
            description: `${appName} connected successfully with ${authData.tools_count || 0} tools available.`,
          });
        }

        // Invalidate React Query cache to refresh agent data
        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
        queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });
      } else {
        throw new Error(authData.error || 'Failed to complete OAuth flow');
      }

      // Invalidate React Query cache to refresh cursor agent selector
      queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });

      // Call success callback
      options.onConnectionSuccess?.(appKey, appName);

    } catch (error: any) {
      console.error('Connection error:', error);
      toast.error("Connection Failed", {
        description: error.message || `Failed to connect to ${appName}`,
      });

      // Call error callback
      options.onConnectionError?.(appKey, appName, error);
    } finally {
      setConnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  }, [connectingApps, getAuthHeaders, options, queryClient]);

  return {
    connectToMCPServer,
    connectingApps,
    isConnecting: (appKey: string) => connectingApps.has(appKey),
  };
}
